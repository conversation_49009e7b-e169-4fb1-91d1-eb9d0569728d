import torch
import torch.nn as nn
import warnings
warnings.filterwarnings("ignore")

"""CVPR2023
基于机器学习的现代技术在自动雷达场景理解方面显示出相当大的潜力。在这些努力中，雷达语义分割 （RSS） 可以提供更精细和详细的信息，包括雷达有效感受野内的移动物体和背景杂波。
受卷积网络在各种视觉计算任务中的成功的推动，这些网络也被引入来解决 RSS 任务。但是，常规卷积运算和修改后的卷积运算都不是专门用于解释雷达信号的。现有卷积的感受野由光信号中的对象表示定义，但这两个信号具有不同的感知机制。
在经典的雷达信号处理中，根据局部峰值响应（即 CFAR 检测）来检测目标特征。受这个想法的启发，我们将卷积运算的感受野重新定义为峰值感受野（PRF），并提出了峰值卷积运算（PeakConv）来学习端到端网络中的对象特征。
通过将提出的 PeakConv 层合并到编码器中，与其他 SoTA 方法相比，我们的 RSS 网络可以在从 FMCW 雷达收集的多视图实测数据集上获得更好的分割结果。
"""


class DoubleConvBlock(nn.Module):
    """ (2D conv => BN => LeakyReLU) * 2 """

    def __init__(self, in_ch, out_ch, k_size, pad, dil):
        super().__init__()
        self.block = nn.Sequential(
            nn.Conv2d(in_ch, out_ch, kernel_size=k_size, padding=pad, dilation=dil),
            nn.BatchNorm2d(out_ch),
            nn.LeakyReLU(inplace=True),
            nn.Conv2d(out_ch, out_ch, kernel_size=k_size, padding=pad, dilation=dil),
            nn.BatchNorm2d(out_ch),
            nn.LeakyReLU(inplace=True)
        )

    def forward(self, x):
        x = self.block(x)
        return x

class DoubleTPKCBlock(nn.Module):
    """ (Temporal PKC block => BN => LeakyReLU) * 2 """
    """ Input: x (B, in_ch, n_frames, h, w) """
    """ Output x (B, out_ch, h, w) """

    def __init__(self, n_frames, in_ch, out_ch, bias, rb, gb_dop, gb, sig):
        super(DoubleTPKCBlock, self).__init__()
        self.n_frames = n_frames
        self.bias = bias
        self.refer_band = rb
        self.guard_band_dop = gb_dop
        self.guard_band = gb
        self.signal_type = sig

        self.tpkc1 = PeakConv2D(in_ch, out_ch, bias=self.bias,
                                refer_band=self.refer_band,
                                guard_band_dop=self.guard_band_dop,
                                guard_band=self.guard_band,
                                signal_type=self.signal_type)
        self.bn1 = nn.BatchNorm2d(out_ch)
        self.act1 = nn.LeakyReLU(inplace=True)

        self.tpkc2 = PeakConv2D(out_ch, out_ch, bias=self.bias,
                                refer_band=self.refer_band,
                                guard_band_dop=self.guard_band_dop,
                                guard_band=self.guard_band,
                                signal_type=self.signal_type)
        self.bn2 = nn.BatchNorm2d(out_ch)
        self.act2 = nn.LeakyReLU(inplace=True)
        self.temp_avg_pool = nn.AvgPool3d((self.n_frames, 1, 1))

    def forward(self, x):
        # (B,in_ch,n_frames,h,w) ==> (B,n_frames,in_ch,h,w)
        x1 = x.contiguous().permute(0, 2, 1, 3, 4)
        # (B,n_frames,in_ch,h,w) ==> (B*n_frames,in_ch,h,w)
        x1 = x1.contiguous().flatten(start_dim=0, end_dim=1)
        # PKC block output: (B*n_frames,out_ch,h,w)
        x1 = self.tpkc1(x1)
        x1 = self.bn1(x1)
        x1 = self.act1(x1)
        x1 = self.tpkc2(x1)
        x1 = self.bn2(x1)
        x1 = self.act2(x1)
        # (B*n_frames,out_ch,h,w) ==> (B,out_ch,n_frames,h,w)
        x1 = x1.contiguous().view(x.size(0), x1.size(1), self.n_frames, x1.size(2), x1.size(3))
        x1 = self.temp_avg_pool(x1)
        return x1


class ConvBlock(nn.Module):
    """ (2D conv => BN => LeakyReLU) """

    def __init__(self, in_ch, out_ch, k_size, pad, dil):
        super().__init__()
        self.block = nn.Sequential(
            nn.Conv2d(in_ch, out_ch, kernel_size=k_size, padding=pad, dilation=dil),
            nn.BatchNorm2d(out_ch),
            nn.LeakyReLU(inplace=True)
        )

    def forward(self, x):
        x = self.block(x)
        return x


# reda peak_conv zlw @20220624
# Peak Conv with fixed guard banwidth (gb) and dense reference receptive field (RRF)
class PeakConv2D(nn.Module):
    def __init__(self, in_ch, out_ch, bias, refer_band, guard_band_dop, guard_band, signal_type):
        """
        Args:
            refer_band: a single integer value or integer vector of two values.
            guard_band_dop: guard bandwidth for doppler input
            guard_band: guard banwidth for non-doppler input
            signal_type in ('range_doppler', 'angle_doppler', 'range_angle').
            k_size, padding and stride are computed using the input fixed guard bandwidth
            input: x in the shape of BxCxHxW
        """
        super(PeakConv2D, self).__init__()
        self.signal_type = signal_type
        if self.signal_type in ('range_doppler', 'angle_doppler'):
            # guard_band should be bi-lateral
            self.guard_band = (guard_band_dop, guard_band_dop)
            # padding order: l-r-t-b
            self.padding = (self.guard_band[1] + refer_band,
                            self.guard_band[1] + refer_band,
                            self.guard_band[0] + refer_band,
                            self.guard_band[0] + refer_band)
        else:
            self.guard_band = (guard_band, guard_band)
            # padding order: l-r-t-b
            self.padding = (self.guard_band[1] + refer_band,
                            self.guard_band[1] + refer_band,
                            self.guard_band[0] + refer_band,
                            self.guard_band[0] + refer_band)
        self.refer_band = refer_band
        self.kernel_size = (self.guard_band[0] + self.guard_band[1] + self.refer_band * 2), 4
        self.stride = self.kernel_size
        # padding order: l-r-t-b
        self.zero_padding = nn.ZeroPad2d(self.padding)
        self.bias = bias
        self.pc_strd = 1
        # conv block for peak receptive field (PRF) in x
        # take PRF as input and output the center point output of PRF;
        # kernel channel = in_ch*2: depth_concat(p_c, p_r) for relation
        # kernel channel = in_ch depth-diff: p_c - p_r for reda
        # since each p_c is expanded as a tensor of kernel scale RF, the stride = kernel_size
        '''
        self.relation_conv = ConvBlock(in_ch*2, out_ch, 
                                       k_size=1,
                                       pad=0, 
                                       dil=1)
                                       '''
        self.peak_conv = nn.Conv2d(in_ch, out_ch,
                                   kernel_size=self.kernel_size,
                                   padding=0,
                                   stride=self.stride,
                                   bias=self.bias)
        self.peak_conv.register_backward_hook(self._set_lr)

    @staticmethod
    # set learning rate for peak_conv
    def _set_lr(module, grad_input, grad_output):
        grad_input = (grad_input[i] * 0.1 for i in range(len(grad_input)))
        grad_output = (grad_output[i] * 0.1 for i in range(len(grad_output)))

    def forward(self, x):
        h, w = x.shape[2], x.shape[3]
        rb = self.refer_band
        gb = self.guard_band
        k_pk = self.kernel_size
        N = k_pk[0] * k_pk[1]
        dtype = x.data.type()
        b = x.size(0)

        # let p_r denote the reference point positions
        # p_r shape: Bx2NxHxW
        p_r = self._get_p_r(b, h, w, N, dtype)
        # sample x_r (b, c, h, w, N) using p_r
        x_r = self._sample_x(self.zero_padding(x), p_r, N)

        # vanilla-pkc
        # x_r = self._reshape_x_prf(k_pk[0], k_pk[1], x_r)
        # x_r = self.peak_conv(x_r)
        # x = x - x_r

        # reda-pkc (b, c, h, w, N) ==> (b, 2c, h, w, N)
        # sample x_c (b, c, h, w, N) using p_c
        p_c = self._get_p_c(b, h, w, N, dtype)
        x_c = self._sample_x(self.zero_padding(x), p_c, N)
        # (b, 2c, h*k_pk[0], w*k_pk[1])
        x = self._reshape_x_prf(k_pk[0], k_pk[1], x_c - x_r)
        # out: (b, c, h, w)
        x = self.peak_conv(x)

        return x

    # getting p_c (the center point coords) from the padded grid of input x
    def _get_p_c(self, b, h, w, N, dtype):
        # generating pc_grid
        p_c_x, p_c_y = torch.meshgrid(
            torch.arange(self.padding[2], h * self.pc_strd + self.padding[2], self.pc_strd),
            torch.arange(self.padding[0], w * self.pc_strd + self.padding[0], self.pc_strd))
        p_c_x = torch.flatten(p_c_x).view(1, 1, h, w).repeat(1, N, 1, 1)
        p_c_y = torch.flatten(p_c_y).view(1, 1, h, w).repeat(1, N, 1, 1)
        # p_c: 1x2NxHxW
        p_c = torch.cat([p_c_x, p_c_y], 1).type(dtype)
        # (B, 2N, h, w)
        p_c = p_c.repeat(b, 1, 1, 1)
        return p_c

    # generating peak receptive field grid
    def _gen_prf_grid(self, rb, gb, N, dtype):
        h_t = -(rb + gb[0])
        h_d = rb + gb[0]
        w_l = -(rb + gb[1])
        w_r = rb + gb[1]
        w_prf = (rb + gb[1]) * 2 + 1
        h_prf = (rb + gb[0]) * 2 + 1

        prf_x_idx, prf_y_idx = torch.meshgrid(
            torch.arange(h_t, h_d + 1),
            torch.arange(w_l, w_r + 1))

        # grid_size*2, 1 grid_size = (h_d*2+1) * (w_r*2+1)
        # taking positions clockwise
        prf_xt = prf_x_idx[0:rb]
        prf_xr = prf_x_idx[rb:(h_prf - 2 * rb + 1), (w_prf - rb):w_prf]
        prf_xd = prf_x_idx[(h_prf - rb):h_prf]
        prf_xl = prf_x_idx[rb:(h_prf - 2 * rb + 1), 0:rb]
        prf_x = torch.cat([torch.flatten(prf_xt),
                           torch.flatten(prf_xr),
                           torch.flatten(prf_xd),
                           torch.flatten(prf_xl)], 0)
        prf_yt = prf_y_idx[0:rb]
        prf_yr = prf_y_idx[rb:(h_prf - 2 * rb + 1), (w_prf - rb):w_prf]
        prf_yd = prf_y_idx[(h_prf - rb):h_prf]
        prf_yl = prf_y_idx[rb:(h_prf - 2 * rb + 1), 0:rb]
        prf_y = torch.cat([torch.flatten(prf_yt),
                           torch.flatten(prf_yr),
                           torch.flatten(prf_yd),
                           torch.flatten(prf_yl)], 0)

        prf = torch.cat([prf_x, prf_y], 0)
        prf = prf.view(1, 2 * N, 1, 1).type(dtype)
        return prf

    # getting p_r positions from each p_c
    def _get_p_r(self, b, h, w, N, dtype):
        rb = self.refer_band
        gb = self.guard_band
        # (1, 2N, 1, 1)
        prf = self._gen_prf_grid(rb, gb, N, dtype)
        # (B, 2N, h, w)
        p_c = self._get_p_c(b, h, w, N, dtype)
        # (B, 2N, h, w)
        p_r = p_c + prf
        return p_r

    # sampling x using p_r or p_c
    def _sample_x(self, x_pad, p, N):
        p = p.contiguous().permute(0, 2, 3, 1).floor()
        b, h, w, _ = p.size()
        h_pad = x_pad.size(2)
        w_pad = x_pad.size(3)
        c = x_pad.size(1)
        # (b, c, h_pad*w_pad)
        # strech each spatial channel of x_pad as 1-D vector
        x_pad = x_pad.contiguous().view(b, c, -1)
        # (b, h_pad, w_pad, N)
        # transform spatial coord of p into the 1-D index
        index = p[..., :N] * w_pad + p[..., N:]
        # (b, c, h*w*N)
        index = index.contiguous().unsqueeze(dim=1).expand(-1, c, -1, -1, -1).contiguous().view(b, c, -1)
        index = index.long()
        x_r = x_pad.gather(dim=-1, index=index).contiguous().view(b, c, h, w, N)
        return x_r

    @staticmethod
    # reshape the x_prf
    def _reshape_x_prf(k_h, k_w, x_prf):
        b, c, h, w, N = x_prf.size()
        x_prf = torch.cat([x_prf[..., s:s + k_w].contiguous().view(b, c, h, w * k_w) for s in range(0, N, k_w)], dim=-1)
        x_prf = x_prf.contiguous().view(b, c, h * k_h, w * k_w)
        return x_prf


if __name__ == '__main__':
    in_ch = 3  # 输入通道数
    out_ch = 3  # 输出通道数
    bias = True  # 是否使用偏置
    refer_band = 1  # 参考带宽
    guard_band_dop = 1  # Doppler 输入的保护带宽
    guard_band = 1  # 非 Doppler 输入的保护带宽
    signal_type = 'range_doppler'  # 可以为 'range_doppler', 'angle_doppler', 'range_angle'


    input_tensor = torch.rand(32, in_ch, 64, 64)

    peak_conv = PeakConv2D(in_ch=in_ch, out_ch=out_ch, bias=bias, refer_band=refer_band, guard_band_dop=guard_band_dop, guard_band=guard_band, signal_type=signal_type)

    output_tensor = peak_conv(input_tensor)

    print("输入张量大小:", input_tensor.size())
    print("输出张量大小:", output_tensor.size())