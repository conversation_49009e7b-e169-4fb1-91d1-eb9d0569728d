import torch
import torch.nn as nn
from einops import rearrange
import math
from timm.models.layers import DropPath, to_2tuple, trunc_normal_

"""CVPR2024
本文提出了一种新颖的运动学和轨迹先验知识增强型 Transformer（KTPFormer），克服了现有基于 Transformer 的 3D 人体姿势估计方法中自注意机制中 QKV 向量的推导都是基于简单线性映射的弱点。
我们提出了两个先验注意模块，即运动学先验注意（KPA）和轨迹先验注意（TPA），以利用已知的人体解剖结构和运动轨迹信息，促进多头自注意中全局依赖关系和特征的有效学习。
KPA 通过构建运动学拓扑来模拟人体中的运动关系，而 TPA 构建轨迹拓扑以学习跨帧的关节运动轨迹信息。
通过利用先验知识产生 QKV 向量，这两个模块使 KTPFormer 能够同时模拟空间和时间相关性。
在三个基准（Human3.6M MPI-INF-3DHP 和 HumanEva）上进行的大量实验表明，与最先进的方法相比，KTPFormer 实现了卓越的性能。
更重要的是，我们的 KPA 和 TPA 模块具有轻量级的即插即用设计，可以集成到各种基于变压器的网络（即基于扩散的网络）中，
从而在计算开销增加很小的情况下提高性能。
"""

class LearnableGraphConv(nn.Module):
    """
    Semantic graph convolution layer
    """

    def __init__(self, in_features, out_features, adj, bias=True):
        super(LearnableGraphConv, self).__init__()
        self.in_features = in_features
        self.out_features = out_features

        self.W = nn.Parameter(torch.zeros(size=(2, in_features, out_features), dtype=torch.float))
        nn.init.xavier_uniform_(self.W.data, gain=1.414)

        self.M = nn.Parameter(torch.ones(size=(adj.size(0), out_features), dtype=torch.float))

        # spatial/temporal local topology
        self.adj = adj

        # simulated spatial/temporal global topology
        self.adj2 = nn.Parameter(torch.ones_like(adj))
        nn.init.constant_(self.adj2, 1e-6)

        if bias:
            self.bias = nn.Parameter(torch.zeros(out_features, dtype=torch.float))
            stdv = 1. / math.sqrt(self.W.size(2))
            self.bias.data.uniform_(-stdv, stdv)
        else:
            self.register_parameter('bias', None)

    def forward(self, input):
        h0 = torch.matmul(input, self.W[0])
        h1 = torch.matmul(input, self.W[1])

        adj = self.adj.to(input.device) + self.adj2.to(input.device)

        adj = (adj.T + adj) / 2

        E = torch.eye(adj.size(0), dtype=torch.float).to(input.device)

        output = torch.matmul(adj * E, self.M * h0) + torch.matmul(adj * (1 - E), self.M * h1)
        if self.bias is not None:
            return output + self.bias.view(1, 1, -1)
        else:
            return output

    def __repr__(self):
        return self.__class__.__name__ + ' (' + str(self.in_features) + ' -> ' + str(self.out_features) + ')'

class KPA(nn.Module):
    def __init__(self, adj, input_dim, output_dim, p_dropout=None):
        super(KPA, self).__init__()

        self.gconv = LearnableGraphConv(input_dim, output_dim, adj)
        self.bn = nn.BatchNorm1d(output_dim)
        self.relu = nn.ReLU()

        if p_dropout is not None:
            self.dropout = nn.Dropout(p_dropout)
        else:
            self.dropout = None

    def forward(self, x):
        x = self.gconv(x).transpose(1, 2)
        x = self.bn(x).transpose(1, 2)
        if self.dropout is not None:
            x = self.dropout(self.relu(x))

        x = self.relu(x)
        return x


class KPAttention(nn.Module):
    def __init__(self, adj, dim, num_heads=8, drop_path=0, drop_rate=0, norm_layer=nn.LayerNorm, qkv_bias=False,
                 qk_scale=None, attn_drop=0., proj_drop=0., comb=False, vis=False):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        # NOTE scale factor was wrong in my original version, can set manually to be compat with prev weights
        self.scale = qk_scale or head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)

        self.kpa = KPA(adj, 2, dim, p_dropout=None)

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)

        self.proj_drop = nn.Dropout(proj_drop)
        self.comb = comb
        self.vis = vis

        self.Spatial_pos_embed = nn.Parameter(torch.zeros(1, 17, dim))

        self.pos_drop = nn.Dropout(p=drop_rate)

        self.norm1 = norm_layer(dim)

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x, vis=False):

        x = self.kpa(x)

        x += self.Spatial_pos_embed

        x = self.pos_drop(x)

        res = x.clone()

        x = self.norm1(x)

        B, N, C = x.shape

        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        # Now x shape (3, B, heads, N, C//heads)
        q, k, v = qkv[0], qkv[1], qkv[2]  # make torchscript happy (cannot use tensor as tuple)
        if self.comb == True:
            attn = (q.transpose(-2, -1) @ k) * self.scale
        elif self.comb == False:
            attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        if self.comb == True:
            x = (attn @ v.transpose(-2, -1)).transpose(-2, -1)
            # print(x.shape)
            x = rearrange(x, 'B H N C -> B N (H C)')
            # print(x.shape)
        elif self.comb == False:
            x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        x = res + self.drop_path(x)

        return x
    
if __name__ == '__main__':
    adj_matrix = torch.eye(17)

    input_dim = 2
    dim = 2

    block = KPAttention(adj_matrix, dim, num_heads=2, drop_path=0.1, drop_rate=0.1)

    input_tensor = torch.rand(4, 17, 2)

    output = block(input_tensor)

    print("Input size:", input_tensor.size())
    print("Output size:", output.size())