import torch
import torch.nn as nn
from einops import rearrange, repeat
import math


"""CVPR2024
基于Transformer的方法在图像修复任务中取得了出色的表现，因为它们能够建模长距离的依赖关系，而这是恢复清晰图像的关键。
尽管各种高效的注意力机制设计已经解决了Transformer使用中与计算密集相关的问题，但它们通常会通过考虑所有可用的tokens，涉及冗余信息和来自不相关区域的噪声交互。
在本文中，我们提出了一种自适应稀疏Transformer（AST），以减轻不相关区域的噪声交互，并在空间和通道域中消除特征冗余。AST由两个核心设计组成，即自适应稀疏自注意力（ASSA）模块和特征精炼前馈网络（FRFN）。
具体而言，ASSA通过一个双分支模式进行自适应计算，其中稀疏分支用于过滤掉查询-键匹配得分较低带来的负面影响，以便在聚合特征时排除干扰，而密集分支则确保网络中的信息流充足，以学习判别性表示。
同时，FRFN采用增强和简化机制来消除通道中的特征冗余，提升潜在清晰图像的修复效果。在常用基准数据集上的实验结果表明，我们的方法在雨痕去除、真实雾霾去除和雨滴去除等多个任务中具有多样性和竞争力。
"""


class FRFN(nn.Module):
    def __init__(self, dim=16, hidden_dim=128, act_layer=nn.GELU ,drop = 0., use_eca=False):
        super().__init__()
        self.linear1 = nn.Sequential(nn.Linear(dim, hidden_dim * 2), act_layer())
        self.dwconv = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim, groups=hidden_dim, kernel_size=3, stride=1, padding=1),
            act_layer())
        self.linear2 = nn.Sequential(nn.Linear(hidden_dim, dim))
        self.dim = dim
        self.hidden_dim = hidden_dim

        self.dim_conv = self.dim // 4
        self.dim_untouched = self.dim - self.dim_conv
        self.partial_conv3 = nn.Conv2d(self.dim_conv, self.dim_conv, 3, 1, 1, bias=False)

    def forward(self, x):
        # bs x hw x c
        bs, hw, c = x.size()
        hh = int(math.sqrt(hw))

        # spatial restore
        x = rearrange(x, 'b (h w) (c) -> b c h w', h=hh, w=hh)

        x1, x2 = torch.split(x, [self.dim_conv, self.dim_untouched], dim=1)
        x1 = self.partial_conv3(x1)
        x = torch.cat((x1, x2), 1)

        # flatten
        x = rearrange(x, 'b c h w -> b (h w) c', h=hh, w=hh)

        x = self.linear1(x)
        # gate mechanism
        x_1, x_2 = x.chunk(2, dim=-1)

        x_1 = rearrange(x_1, 'b (h w) (c) -> b c h w', h=hh, w=hh)
        x_1 = self.dwconv(x_1)
        x_1 = rearrange(x_1, 'b c h w -> b (h w) c', h=hh, w=hh)
        x = x_1 * x_2

        x = self.linear2(x)
        # x = self.eca(x)

        return x

if __name__ == '__main__':
    block = FRFN()
    input = torch.rand(1, 64, 16)
    output = block(input)
    print(f"Input size: {input.size()}")
    print(f"Output size: {output.size()}")
