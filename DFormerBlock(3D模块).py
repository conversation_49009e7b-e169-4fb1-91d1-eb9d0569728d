from torch import nn
import torch
import torch.nn.functional
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint
from functools import reduce
from operator import mul
from timm.models.layers import DropPath, to_3tuple, trunc_normal_
NEG_INF = -1000000


"""Arxiv2023
计算机辅助医学图像分割已在诊断和治疗中得到广泛应用，以获得靶器官和组织的形状和体积的临床有用信息。
在过去几年中，基于卷积神经网络（CNN）的方法（例如U-Net）在这一领域占据主导地位，但仍然存在长距离信息捕获不足的问题。
因此，最近的工作提出了用于医学图像分割任务的计算机视觉Transformer变体，并获得了有希望的性能。
这种 Transformer 通过计算成对的补丁关系来对长程依赖性进行建模。
然而，它们的计算成本高得令人望而却步，尤其是在 3D 医学图像（例如 CT 和 MRI）上。
在本文中，我们提出了一种称为膨胀变压器的新方法，该方法对在局部和全局范围内交替捕获的成对补丁关系进行自注意力。
受膨胀卷积核的启发，我们以膨胀的方式进行全局自注意力，在不增加所涉及的补丁的情况下扩大感受野，从而降低计算成本。
基于这种Dilated Transformer的设计，我们构建了一种U型编码器-解码器分层结构，称为D-Former，用于3D医学图像分割。
在Synapse和ACDC数据集上的实验表明，我们从头开始训练的D-Former模型以较低的计算成本优于各种基于CNN或Transformer的竞争性分割模型，
而无需耗时的每次训练过程。
"""

class Mlp(nn.Module):
    """ Multilayer perceptron."""

    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class Attention(nn.Module):
    """ Group based multi-head self attention (W-MSA) module with relative position bias.
    It supports both of shifted and non-shifted group.
    Args:
        dim (int): Number of input channels.
        group_size (tuple[int]): The temporal length, height and width of the group.
        num_heads (int): Number of attention heads.
        qkv_bias (bool, optional):  If True, add a learnable bias to query, key, value. Default: True
        qk_scale (float | None, optional): Override default qk scale of head_dim ** -0.5 if set
        attn_drop (float, optional): Dropout ratio of attention weight. Default: 0.0
        proj_drop (float, optional): Dropout ratio of output. Default: 0.0
    """

    def __init__(self, dim, group_size, num_heads, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0.):

        super().__init__()
        self.dim = dim
        self.group_size = group_size  # Gd, Gh, Gw
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, mask=None):
        """ Forward function.
        Args:
            x: input features with shape of (num_groups*B, N, C)
            mask: (0/-inf) mask with shape of (num_groups, N, N) or None
        """
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # B_, nH, N, C

        q = q * self.scale
        attn = q @ k.transpose(-2, -1)

        if mask is not None:
            nG = mask.shape[0]
            attn = attn.view(B_ // nG, nG, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0) # (B, nG, nHead, N, N)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = self.softmax(attn)
        else:
            attn = self.softmax(attn)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

class DFormerBlock3D(nn.Module):
    """ D-Former Block.
    Args:
        dim (int): Number of input channels.
        num_heads (int): Number of attention heads.
        group_size (tuple[int]): Group size.
        shift_size (tuple[int]): Shift size for SW-MSA.
        mlp_ratio (float): Ratio of mlp hidden dim to embedding dim.
        qkv_bias (bool, optional): If True, add a learnable bias to query, key, value. Default: True
        qk_scale (float | None, optional): Override default qk scale of head_dim ** -0.5 if set.
        drop (float, optional): Dropout rate. Default: 0.0
        attn_drop (float, optional): Attention dropout rate. Default: 0.0
        drop_path (float, optional): Stochastic depth rate. Default: 0.0
        act_layer (nn.Module, optional): Activation layer. Default: nn.GELU
        norm_layer (nn.Module, optional): Normalization layer.  Default: nn.LayerNorm
    """

    def __init__(self, dim, num_heads, group_size=(2, 7, 7), interval=8, gsm=0,
                 mlp_ratio=4., qkv_bias=True, qk_scale=None, drop=0., attn_drop=0., drop_path=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm, use_checkpoint=False):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.group_size = group_size
        self.mlp_ratio = mlp_ratio
        self.use_checkpoint = use_checkpoint
        self.gsm = gsm
        self.interval = interval


        self.norm1 = norm_layer(dim)
        self.attn = Attention(dim, group_size=self.group_size, num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)

        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

    def forward_part1(self, x):
        B, D, H, W, C = x.shape

        x = self.norm1(x)

        if H < self.group_size[1]:
            # if group size is larger than input resolution, we don't partition group
            self.gsm = 0
            self.group_size = (D, H, W)
        # pad feature maps to multiples of group size
        size_div = self.interval if self.gsm == 1 else self.group_size
        if isinstance(size_div, int): size_div = to_3tuple(size_div)
        pad_l = pad_t = pad_d0 = 0
        pad_d = (size_div[0] - D % size_div[0]) % size_div[0]
        pad_b = (size_div[1] - H % size_div[1]) % size_div[1]
        pad_r = (size_div[2] - W % size_div[2]) % size_div[2]
        x = F.pad(x, (0, 0, pad_l, pad_r, pad_t, pad_b, pad_d0, pad_d))
        _, Dp, Hp, Wp, _ = x.shape

        mask = torch.zeros((1, Dp, Hp, Wp, 1), device=x.device)
        if pad_d > 0:
            mask[:, -pad_d:, :, :, :] = -1
        if pad_b > 0:
            mask[:, :, -pad_b:, :, :] = -1
        if pad_r > 0:
            mask[:, :, :, -pad_r:, :] = -1

        # group embeddings and generate attn_mask
        if self.gsm == 0: # LS-MSA
            Gd = size_div[0]
            Gh = size_div[1]
            Gw = size_div[2]
            B, D2, H2, W2, C = x.shape

            x = x.view(B, D2 // Gd, Gd, H2 // Gh, Gh, W2 // Gw, Gw, C).permute(0, 1, 3, 5, 2, 4, 6, 7).contiguous()
            x = x.reshape(-1, reduce(mul, size_div), C)

            nG = (Dp * Hp * Wp) // (Gd * Gh * Gw)  # group_num

            if pad_r > 0 or pad_b > 0 or pad_d > 0:
                mask = mask.reshape(1, Dp // Gd, Gd, Hp // Gh, Gh, Wp // Gw, Gw, 1).permute(0, 1, 3, 5, 2, 4, 6, 7).contiguous()

                mask = mask.reshape(nG, 1, Gd * Gh * Gw)
                attn_mask = torch.zeros((nG, Gd * Gh * Gw, Gd * Gh * Gw), device=x.device)
                attn_mask = attn_mask.masked_fill(mask < 0, NEG_INF)
            else:
                attn_mask = None

        else: # GS-MSA
            B, D2, H2, W2, C = x.shape
            interval_d = Dp // self.group_size[0]
            interval_h = Hp // self.group_size[1]
            interval_w = Wp // self.group_size[2]

            Id, Ih, Iw = interval_d, interval_h, interval_w
            Gd, Gh, Gw = Dp // interval_d, Hp // interval_h, Wp // interval_w
            x = x.reshape(B, Gd, Id, Gh, Ih, Gw, Iw, C).permute(0, 2, 4, 6, 1, 3, 5, 7).contiguous()
            x = x.reshape(B * Id * Ih * Iw, Gd * Gh * Gw, C)

            nG = interval_d * interval_h * interval_w  # group_num

            # attn_mask
            if pad_r > 0 or pad_b > 0:
                mask = mask.reshape(1, Gd, Id, Gh, Ih, Gw, Iw, 1).permute(0, 2, 4, 6, 1, 3, 5, 7).contiguous()
                mask = mask.reshape(nG, 1, Gd * Gh * Gw)
                attn_mask = torch.zeros((nG, Gd * Gh * Gw, Gd * Gh * Gw), device=x.device)
                attn_mask = attn_mask.masked_fill(mask < 0, NEG_INF)
            else:
                attn_mask = None

        # multi-head self-attention
        x = self.attn(x, mask=attn_mask)

        # ungroup embeddings
        if self.gsm == 0:
            x = x.reshape(B, D2 // size_div[0], H2 // size_div[1], W2 // size_div[2], size_div[0], size_div[1],
                       size_div[2], C).permute(0, 1, 4, 2, 5, 3, 6, 7).contiguous() # B, Hp//G, G, Wp//G, G, C
            x = x.view(B, D2, H2, W2, -1)
        else:
            x = x.reshape(B, interval_d, interval_h, interval_w,
                          D2 // interval_d, H2 // interval_h, W2 // interval_w, C)\
                .permute(0, 4, 1, 5, 2, 6, 3, 7).contiguous() # B, Gh, I, Gw, I, C

            x = x.view(B, D2, H2, W2, -1)

        # remove padding
        if pad_d > 0 or pad_r > 0 or pad_b > 0:
            x = x[:, :D, :H, :W, :].contiguous()

        return x

    def forward_part2(self, x):
        return self.drop_path(self.mlp(self.norm2(x)))

    def forward(self, x):
        """ Forward function.
        Args:
            x: Input feature, tensor size (B, D, H, W, C).
            mask_matrix: Attention mask for cyclic shift.
        """
        shortcut = x
        if self.use_checkpoint:
            x = checkpoint.checkpoint(self.forward_part1, x)
        else:
            x = self.forward_part1(x)
        x = shortcut + self.drop_path(x)

        if self.use_checkpoint:
            x = x + checkpoint.checkpoint(self.forward_part2, x)
        else:
            x = x + self.forward_part2(x)

        return x

if __name__ == '__main__':
    block = DFormerBlock3D(
        dim=64,
        num_heads=4,
        group_size=(2, 4, 4),  # Group size (D, H, W)
        interval=8,
        gsm=0,
        mlp_ratio=4.0,
        qkv_bias=True,
        drop=0.1,
        attn_drop=0.1,
        drop_path=0.1,
        act_layer=nn.GELU,
        norm_layer=nn.LayerNorm
    )

    # (B, D, H, W, C)
    input = torch.rand(1, 16, 32, 32, 64)

    output = block(input)

    print("Input size:", input.size())
    print("Output size:", output.size())