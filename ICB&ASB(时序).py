import pytorch_lightning as pl
import torch
import torch.nn as nn
from timm.models.layers import trunc_normal_

"""ICML2024《TSLANet: Rethinking Transformers for Time Series Representation Learning》
时间序列数据以其内在的长程和短程依赖性为特征，对分析应用提出了独特的挑战。虽然基于 Transformer 的模型擅长捕捉长程依赖性，但它们在噪声敏感性、计算效率和较小数据集的过度拟合方面面临限制。
为此，我们引入了一种新型时间序列轻量级自适应网络 (TSLANet)，作为适用于各种时间序列任务的通用卷积模型。
具体来说，我们提出了一种自适应频谱块(ASB)，利用傅里叶分析来增强特征表示并捕获长期和短期交互，同时通过自适应阈值减轻噪声。
此外，我们引入了一个交互式卷积(ICB)并利用自监督学习来改进 TSLANet 解码复杂时间模式的能力并提高其在不同数据集上的稳健性。
我们的全面实验表明，TSLANet 在分类、预测和异常检测等各种任务中的表现都优于最先进的模型，展示了其在各种噪声水平和数据大小范围内的弹性和适应性。
"""

class ICB(pl.LightningModule):
    def __init__(self, in_features, hidden_features, drop=0.):
        super().__init__()
        self.conv1 = nn.Conv1d(in_features, hidden_features, 1)
        self.conv2 = nn.Conv1d(in_features, hidden_features, 3, 1, padding=1)
        self.conv3 = nn.Conv1d(hidden_features, in_features, 1)
        self.drop = nn.Dropout(drop)
        self.act = nn.GELU()

    def forward(self, x):
        x = x.transpose(1, 2)
        x1 = self.conv1(x)
        x1_1 = self.act(x1)
        x1_2 = self.drop(x1_1)

        x2 = self.conv2(x)
        x2_1 = self.act(x2)
        x2_2 = self.drop(x2_1)

        out1 = x1 * x2_2
        out2 = x2 * x1_2

        x = self.conv3(out1 + out2)
        x = x.transpose(1, 2)
        return x


if __name__ == '__main__':
    in_features = 64         # 输入特征数
    hidden_features = 128    # 隐藏特征数
    drop = 0.1

    block = ICB(in_features=in_features, hidden_features=hidden_features, drop=drop)


    batch_size = 8
    seq_len = 50
    input_tensor = torch.rand(batch_size, seq_len, in_features)  # (B, L, C)

    output = block(input_tensor)

    print("输入的形状:", input_tensor.size())
    print("输出的形状:", output.size())



class Adaptive_Spectral_Block(nn.Module):
    def __init__(self, dim, adaptive_filter=True):
        super().__init__()
        self.complex_weight_high = nn.Parameter(torch.randn(dim, 2, dtype=torch.float32) * 0.02)
        self.complex_weight = nn.Parameter(torch.randn(dim, 2, dtype=torch.float32) * 0.02)

        trunc_normal_(self.complex_weight_high, std=.02)
        trunc_normal_(self.complex_weight, std=.02)
        self.threshold_param = nn.Parameter(torch.rand(1))  # * 0.5)
        self.adaptive_filter = adaptive_filter  # 设置 adaptive_filter 选项

    def create_adaptive_high_freq_mask(self, x_fft):
        B, _, _ = x_fft.shape

        # 计算频域中的能量
        energy = torch.abs(x_fft).pow(2).sum(dim=-1)

        # 将能量展平，并计算中位数
        flat_energy = energy.view(B, -1)  # 展平 H 和 W 到单一维度
        median_energy = flat_energy.median(dim=1, keepdim=True)[0]  # 计算中位数
        median_energy = median_energy.view(B, 1)  # 重塑维度匹配原始形状

        # 归一化能量
        normalized_energy = energy / (median_energy + 1e-6)

        adaptive_mask = ((normalized_energy > self.threshold_param).float() - self.threshold_param).detach() + self.threshold_param
        adaptive_mask = adaptive_mask.unsqueeze(-1)

        return adaptive_mask

    def forward(self, x_in):
        B, N, C = x_in.shape

        dtype = x_in.dtype
        x = x_in.to(torch.float32)

        # 沿时间维度应用 FFT
        x_fft = torch.fft.rfft(x, dim=1, norm='ortho')
        weight = torch.view_as_complex(self.complex_weight)
        x_weighted = x_fft * weight

        if self.adaptive_filter:
            # 自适应高频掩码
            freq_mask = self.create_adaptive_high_freq_mask(x_fft)
            x_masked = x_fft * freq_mask.to(x.device)

            weight_high = torch.view_as_complex(self.complex_weight_high)
            x_weighted2 = x_masked * weight_high

            x_weighted += x_weighted2

        # 应用逆 FFT
        x = torch.fft.irfft(x_weighted, n=N, dim=1, norm='ortho')

        x = x.to(dtype)
        x = x.view(B, N, C)  # 重塑回原始形状

        return x

if __name__ == '__main__':
    dim = 64
    block = Adaptive_Spectral_Block(dim=dim)


    batch_size = 8
    seq_len = 50
    input_tensor = torch.rand(batch_size, seq_len, dim)  # 输入形状 (B, N, C)

    output = block(input_tensor)

    print("输入的形状:", input_tensor.size())
    print("输出的形状:", output.size())