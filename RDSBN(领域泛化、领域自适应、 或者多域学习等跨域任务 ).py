from torch.nn.modules.batchnorm import _BatchNorm
from torch.nn.parameter import Parameter
from torch.nn import functional as F
from torch import nn
import torch

"""Unsupervised Multi-Source Domain Adaptation for Person Re-Identification(CVPR2021) 
适用于有领域差异的场景，特别是在领域自适应、多域学习和跨域任务中.

无监督领域自适应（UDA）方法用于行人再识别（re-ID），其目标是将来自有标签的源数据的 re-ID 知识转移到无标签的目标数据上。
尽管取得了显著进展，但大多数方法仅使用来自单一源域的有限数据进行模型预训练，从而未能充分利用丰富的有标签数据。为充分利用这些宝贵的有标签数据，我们将多源概念引入到 UDA 行人再识别领域，在训练中使用多个源数据集。
然而，由于不同域之间存在域间差距，简单地结合不同数据集仅能带来有限的提升。在本文中，我们从两个角度出发来解决该问题，即领域特定视角和领域融合视角。为此，我们提出了两个结构性模块，且它们彼此兼容。
首先，提出了一个校正域特定批归一化（RDSBN）模块(rectification domain-specific batch normalization)，以同时减少域特定特征并增强行人特征的区分性。其次，提出了基于图卷积网络（GCN）的多域信息融合（MDIF）模块，通过融合不同域的特征来最小化域间距离。
所提出的方法显著超越了当前最先进的 UDA 行人再识别方法，甚至在不使用任何后处理技术的情况下达到了与监督方法相当的性能。
"""


class RcBatchNorm2d(_BatchNorm):
    def __init__(self, num_features, eps=1e-5, momentum=0.1, affine=True, track_running_stats=True):
        super(RcBatchNorm2d, self).__init__(num_features, eps, momentum, affine, track_running_stats)

        self.cfc = Parameter(torch.Tensor(num_features, 2))
        self.cfc.data.fill_(0)
        self.activation = nn.Sigmoid()

    def _check_input_dim(self, input):
        if input.dim() != 4:
            raise ValueError('expected 4D input (got {}D input)'.format(input.dim()))

    def recalibration(self, x, eps=1e-5):
        N, C, _, _ = x.size()

        channel_mean = x.view(N, C, -1).mean(dim=2, keepdim=True)
        channel_var = x.view(N, C, -1).var(dim=2, keepdim=True) + eps
        channel_std = channel_var.sqrt()

        t = torch.cat((channel_mean, channel_std), dim=2)

        z = t * self.cfc[None, :, :]  # B x C x 2
        z = torch.sum(z, dim=2)[:, :, None, None]  # B x C x 1 x 1
        g = self.activation(z)

        return g

    def forward(self, input, epochs=-1):
        self._check_input_dim(input)

        out_bn = F.batch_norm(
            input, self.running_mean, self.running_var, self.weight, self.bias,
            self.training, self.momentum, self.eps)

        if not self.training and epochs == 0:
            # print('epoch 0 for stable clustering.')
            out = out_bn
        else:
            g = self.recalibration(input)
            out = out_bn * g

        return out


class DomainSpecificRcBatchNorm2d(nn.Module):

    def __init__(self, num_channel, num_domains, eps=1e-9, momentum=0.1, affine=True,
                 track_running_stats=True, within_single_batch=False):
        super(DomainSpecificRcBatchNorm2d, self).__init__()
        self.bns = nn.ModuleList(
            [RcBatchNorm2d(num_channel, eps, momentum, affine, track_running_stats) for _ in range(num_domains)])
        self.single_batch = within_single_batch

    def reset_running_stats(self):
        for bn in self.bns:
            bn.reset_running_stats()

    def reset_parameters(self):
        for bn in self.bns:
            bn.reset_parameters()

    def _check_input_dim(self, input):
        if input.dim() != 4:
            raise ValueError('expected 4D input (got {}D input)'.format(input.dim()))

    def bias_requires_grad(self, flag):
        for bn in self.bns:
            bn.bias.requires_grad_(flag)

    def forward(self, x, domain_label):
        self._check_input_dim(x)
        N, C, _, _ = x.size()

        epochs = -1
        if x.size(0) != domain_label.size(0):
            epochs = domain_label[-1]

        if self.training and self.single_batch:
            stride = 16
            assert stride * 4 == N

            assert domain_label[0] == 0
            bn0 = self.bns[0]

            assert domain_label[stride] == 1
            bn1 = self.bns[1]

            assert domain_label[2 * stride] == 2
            bn2 = self.bns[2]

            assert domain_label[3 * stride] == 3
            bn3 = self.bns[3]

            out = [bn0(x[:stride], epochs), bn1(x[stride:2*stride], epochs),
                   bn2(x[2 * stride: 3 * stride], epochs),
                   bn3(x[3 * stride:], epochs)]
            out = torch.cat(out)
        else:
            bn = self.bns[domain_label[0]]
            out = bn(x, epochs)

        return out, domain_label

if __name__ == '__main__':
    num_channels = 3
    num_domains = 4
    batch_size = 16  # 每个域 4 个样本，总共 16 个样本
    height, width = 32, 32


    block = DomainSpecificRcBatchNorm2d(num_channel=num_channels, num_domains=num_domains, within_single_batch=True)

    # 创建一个随机输入张量和域标签
    input_tensor = torch.rand(batch_size * num_domains, num_channels, height, width)  # (64, 3, 32, 32)
    domain_labels = torch.cat([torch.full((batch_size,), i) for i in range(num_domains)])  # (64,)

    output_tensor, output_labels = block(input_tensor, domain_labels)

    print("输入张量大小:", input_tensor.size())
    print("输出张量大小:", output_tensor.size())
    print("域标签大小:", output_labels.size())